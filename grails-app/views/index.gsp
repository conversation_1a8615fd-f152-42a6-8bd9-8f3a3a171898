<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>WonderSlate V2 - Welcome</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 40px;
        }

        .welcome-text {
            color: #333;
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn-group {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .features {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }

        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            color: #555;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        @media (max-width: 600px) {
            .container {
                padding: 40px 20px;
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="logo">WonderSlate V2</div>
        <div class="subtitle">Your Learning Platform</div>

        <div class="welcome-text">
            Welcome to WonderSlate V2! Experience a new way of learning and collaboration.
            Join our community of learners, educators, and innovators.
        </div>

        <div class="btn-group">
            <a href="${createLink(controller: 'authentication', action: 'login')}" class="btn btn-primary">
                Login
            </a>
            <a href="${createLink(controller: 'authentication', action: 'register')}" class="btn btn-secondary">
                Register
            </a>
        </div>

        <div class="features">
            <h3 style="color: #333; margin-bottom: 20px;">Why Choose WonderSlate?</h3>
            <div class="feature-list">
                <div class="feature">
                    <div class="feature-icon">📚</div>
                    <h4>Rich Content</h4>
                    <p>Access diverse learning materials</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">👥</div>
                    <h4>Community</h4>
                    <p>Connect with fellow learners</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎯</div>
                    <h4>Personalized</h4>
                    <p>Tailored learning experience</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>