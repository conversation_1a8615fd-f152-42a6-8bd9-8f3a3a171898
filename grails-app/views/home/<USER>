<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Dashboard - WonderSlate V2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .welcome-text {
            font-size: 1.1rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .card p {
            color: #666;
            line-height: 1.6;
        }
        
        .welcome-section {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .welcome-section h1 {
            color: #333;
            margin-bottom: 15px;
            font-size: 2.2rem;
        }
        
        .welcome-section p {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .user-details {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .user-details h2 {
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .user-info {
                flex-direction: column;
                gap: 10px;
            }
            
            .main-content {
                padding: 0 15px;
            }
            
            .welcome-section {
                padding: 30px 20px;
            }
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">WonderSlate V2</div>
            <div class="user-info">
                <div class="welcome-text">Welcome, ${user?.name ?: user?.username}!</div>
                <a href="${createLink(controller: 'authentication', action: 'logout')}" class="logout-btn">Logout</a>
            </div>
        </div>
    </div>
    
    <div class="main-content">
        <div class="welcome-section">
            <h1>Welcome to Your Dashboard</h1>
            <p>You have successfully logged into WonderSlate V2. Explore the features and start your learning journey!</p>
        </div>
        
        <div class="user-details">
            <h2>Your Profile Information</h2>
            <div class="detail-item">
                <span><strong>Username:</strong></span>
                <span>${user?.username}</span>
            </div>
            <div class="detail-item">
                <span><strong>Name:</strong></span>
                <span>${user?.name ?: 'Not provided'}</span>
            </div>
            <div class="detail-item">
                <span><strong>Email:</strong></span>
                <span>${user?.email ?: 'Not provided'}</span>
            </div>
            <div class="detail-item">
                <span><strong>Account Status:</strong></span>
                <span>${user?.enabled ? 'Active' : 'Inactive'}</span>
            </div>
            <div class="detail-item">
                <span><strong>Member Since:</strong></span>
                <span><g:formatDate date="${user?.dateCreated}" format="MMM dd, yyyy" /></span>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <div class="card-icon">📚</div>
                <h3>Learning Materials</h3>
                <p>Access your courses, lessons, and educational content. Continue where you left off or explore new topics.</p>
            </div>
            
            <div class="card">
                <div class="card-icon">👥</div>
                <h3>Community</h3>
                <p>Connect with other learners, join discussions, and collaborate on projects with the WonderSlate community.</p>
            </div>
            
            <div class="card">
                <div class="card-icon">📊</div>
                <h3>Progress Tracking</h3>
                <p>Monitor your learning progress, view achievements, and track your performance across different subjects.</p>
            </div>
            
            <div class="card">
                <div class="card-icon">⚙️</div>
                <h3>Settings</h3>
                <p>Customize your profile, update preferences, and manage your account settings for a personalized experience.</p>
            </div>
        </div>
    </div>
</body>
</html>
