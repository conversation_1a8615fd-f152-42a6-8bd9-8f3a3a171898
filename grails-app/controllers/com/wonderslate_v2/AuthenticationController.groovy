package com.wonderslate_v2

import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService

class AuthenticationController {

    SpringSecurityService springSecurityService
    AuthenticationService authenticationService

    def index() {
        redirect(action: 'login')
    }

    def login() {
        if (springSecurityService.isLoggedIn()) {
            redirect(controller: 'home', action: 'index')
            return
        }
        [:]
    }

    def authenticate(){
        def requestBody = request.JSON
        String username = requestBody.username
        String password = requestBody.password
        try {
            def user = authenticationService.authenticate(username, password, session)
            def json = [status: "success", statusCode: 200, user: user.username, message: "Authentication successful for user: ${user.username}"]
            render(json as JSON)
        }catch (Exception e){
            println(e.message)
            def json = [status: "error", statusCode: 401, message: e.message]
            render(json as JSON)
        }
    }

    def register() {
        if (springSecurityService.isLoggedIn()) {
            redirect(controller: 'home', action: 'index')
            return
        }
        [:]
    }

    def signup() {
        def requestBody = request.JSON
        String name = requestBody.name
        String username = requestBody.username
        String email = requestBody.email
        String password = requestBody.password
        String confirmPassword = requestBody.confirmPassword

        try {
            def user = authenticationService.registerUser(name, username, email, password, confirmPassword)
            def json = [status: "success", statusCode: 201, user: user.username, message: "User registration successful for: ${user.username}"]
            render(json as JSON)
        } catch (Exception e) {
            println(e.message)
            def json = [status: "error", statusCode: 400, message: e.message]
            render(json as JSON)
        }
    }

    def logout() {
        session.user = null
        session.invalidate()
        redirect(controller: 'authentication', action: 'login')
    }

}
