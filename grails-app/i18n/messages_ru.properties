default.doesnt.match.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ ÑÐ¾Ð¾ÑÐ²ÐµÑÑÑÐ²ÑÐµÑ Ð¾Ð±ÑÐ°Ð·ÑÑ [{3}]
default.invalid.url.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ URL-Ð°Ð´ÑÐµÑÐ¾Ð¼
default.invalid.creditCard.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ Ð½Ð¾Ð¼ÐµÑÐ¾Ð¼ ÐºÑÐµÐ´Ð¸ÑÐ½Ð¾Ð¹ ÐºÐ°ÑÑÑ
default.invalid.email.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ e-mail Ð°Ð´ÑÐµÑÐ¾Ð¼
default.invalid.range.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ Ð¿Ð¾Ð¿Ð°Ð´Ð°ÐµÑ Ð² Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ Ð¸Ð½ÑÐµÑÐ²Ð°Ð» Ð¾Ñ [{3}] Ð´Ð¾ [{4}]
default.invalid.size.message=Ð Ð°Ð·Ð¼ÐµÑ Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] (Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ: [{2}]) Ð½Ðµ Ð¿Ð¾Ð¿Ð°Ð´Ð°ÐµÑ Ð² Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ Ð¸Ð½ÑÐµÑÐ²Ð°Ð» Ð¾Ñ [{3}] Ð´Ð¾ [{4}]
default.invalid.max.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð±Ð¾Ð»ÑÑÐµ ÑÐµÐ¼ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ [{3}]
default.invalid.min.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð¼ÐµÐ½ÑÑÐµ ÑÐµÐ¼ Ð¼Ð¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð¾ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ðµ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ [{3}]
default.invalid.max.size.message=Ð Ð°Ð·Ð¼ÐµÑ Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] (Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ: [{2}]) Ð±Ð¾Ð»ÑÑÐµ ÑÐµÐ¼ Ð¼Ð°ÐºÑÐ¸Ð¼Ð°Ð»ÑÐ½Ð¾ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ ÑÐ°Ð·Ð¼ÐµÑ [{3}]
default.invalid.min.size.message=Ð Ð°Ð·Ð¼ÐµÑ Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] (Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ: [{2}]) Ð¼ÐµÐ½ÑÑÐµ ÑÐµÐ¼ Ð¼Ð¸Ð½Ð¸Ð¼Ð°Ð»ÑÐ½Ð¾ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¹ ÑÐ°Ð·Ð¼ÐµÑ [{3}]
default.invalid.validator.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾
default.not.inlist.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ Ð¿Ð¾Ð¿Ð°Ð´Ð°ÐµÑ Ð² ÑÐ¿Ð¸ÑÐ¾Ðº Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ð¹ [{3}]
default.blank.message=ÐÐ¾Ð»Ðµ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ Ð¿ÑÑÑÑÐ¼
default.not.equal.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð±ÑÑÑ ÑÐ°Ð²Ð½Ð¾ [{3}]
default.null.message=ÐÐ¾Ð»Ðµ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð½Ðµ Ð¼Ð¾Ð¶ÐµÑ Ð¸Ð¼ÐµÑÑ Ð·Ð½Ð°ÑÐµÐ½Ð¸Ðµ null
default.not.unique.message=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ [{2}] Ð¿Ð¾Ð»Ñ [{0}] ÐºÐ»Ð°ÑÑÐ° [{1}] Ð´Ð¾Ð»Ð¶Ð½Ð¾ Ð±ÑÑÑ ÑÐ½Ð¸ÐºÐ°Ð»ÑÐ½ÑÐ¼

default.paginate.prev=ÐÑÐµÐ´ÑÐ´ÑÑÐ°Ñ ÑÑÑÐ°Ð½Ð¸ÑÐ°
default.paginate.next=Ð¡Ð»ÐµÐ´ÑÑÑÐ°Ñ ÑÑÑÐ°Ð½Ð¸ÑÐ°

# ÐÑÐ¸Ð±ÐºÐ¸ Ð¿ÑÐ¸ Ð¿ÑÐ¸ÑÐ²Ð¾ÐµÐ½Ð¸Ð¸ Ð´Ð°Ð½Ð½ÑÑ. ÐÐ»Ñ ÑÐ¾ÑÐ½Ð¾Ð¹ Ð½Ð°ÑÑÑÐ¾Ð¹ÐºÐ¸ Ð´Ð»Ñ Ð¿Ð¾Ð»ÐµÐ¹ ÐºÐ»Ð°ÑÑÐ¾Ð² Ð¸ÑÐ¿Ð¾Ð»ÑÐ·ÑÐ¹ÑÐµ
# ÑÐ¾ÑÐ¼Ð°Ñ "typeMismatch.$className.$propertyName" (Ð½Ð°Ð¿ÑÐ¸Ð¼ÐµÑ, typeMismatch.Book.author)
typeMismatch.java.net.URL=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ URL
typeMismatch.java.net.URI=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ URI
typeMismatch.java.util.Date=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼Ð¾Ð¹ Ð´Ð°ÑÐ¾Ð¹
typeMismatch.java.lang.Double=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ ÑÐ¸ÑÐ»Ð¾Ð¼
typeMismatch.java.lang.Integer=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ ÑÐ¸ÑÐ»Ð¾Ð¼
typeMismatch.java.lang.Long=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ ÑÐ¸ÑÐ»Ð¾Ð¼
typeMismatch.java.lang.Short=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ ÑÐ¸ÑÐ»Ð¾Ð¼
typeMismatch.java.math.BigDecimal=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ ÑÐ¸ÑÐ»Ð¾Ð¼
typeMismatch.java.math.BigInteger=ÐÐ½Ð°ÑÐµÐ½Ð¸Ðµ Ð¿Ð¾Ð»Ñ {0} Ð½Ðµ ÑÐ²Ð»ÑÐµÑÑÑ Ð´Ð¾Ð¿ÑÑÑÐ¸Ð¼ÑÐ¼ ÑÐ¸ÑÐ»Ð¾Ð¼
