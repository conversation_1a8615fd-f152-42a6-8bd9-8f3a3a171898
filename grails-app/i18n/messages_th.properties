default.doesnt.match.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¸à¸¹à¸à¸à¹à¸­à¸à¸à¸²à¸¡à¸£à¸¹à¸à¹à¸à¸à¸à¸µà¹à¸à¸³à¸«à¸à¸à¹à¸§à¹à¹à¸ [{3}]
default.invalid.url.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¸à¸¹à¸à¸à¹à¸­à¸à¸à¸²à¸¡à¸£à¸¹à¸à¹à¸à¸ URL
default.invalid.creditCard.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¸à¸¹à¸à¸à¹à¸­à¸à¸à¸²à¸¡à¸£à¸¹à¸à¹à¸à¸à¸«à¸¡à¸²à¸¢à¹à¸¥à¸à¸à¸±à¸à¸£à¹à¸à¸£à¸à¸´à¸
default.invalid.email.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¸à¸¹à¸à¸à¹à¸­à¸à¸à¸²à¸¡à¸£à¸¹à¸à¹à¸à¸à¸­à¸µà¹à¸¡à¸¥à¹
default.invalid.range.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¹à¸à¹à¸¡à¸µà¸à¹à¸²à¸à¸µà¹à¸à¸¹à¸à¸à¹à¸­à¸à¹à¸à¸à¹à¸§à¸à¸à¸²à¸ [{3}] à¸à¸¶à¸ [{4}]
default.invalid.size.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¹à¸à¹à¸¡à¸µà¸à¸à¸²à¸à¸à¸µà¹à¸à¸¹à¸à¸à¹à¸­à¸à¹à¸à¸à¹à¸§à¸à¸à¸²à¸ [{3}] à¸à¸¶à¸ [{4}]
default.invalid.max.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¸¡à¸µà¸à¹à¸²à¹à¸à¸´à¸à¸à¸§à¹à¸²à¸à¹à¸²à¸¡à¸²à¸à¸ªà¸¸à¸ [{3}]
default.invalid.min.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¸¡à¸µà¸à¹à¸²à¸à¹à¸­à¸¢à¸à¸§à¹à¸²à¸à¹à¸²à¸à¹à¸³à¸ªà¸¸à¸  [{3}]
default.invalid.max.size.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¸¡à¸µà¸à¸à¸²à¸à¹à¸à¸´à¸à¸à¸§à¹à¸²à¸à¸à¸²à¸à¸¡à¸²à¸à¸ªà¸¸à¸à¸à¸­à¸ [{3}]
default.invalid.min.size.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¸¡à¸µà¸à¸à¸²à¸à¸à¹à¸³à¸à¸§à¹à¸²à¸à¸à¸²à¸à¸à¹à¸³à¸ªà¸¸à¸à¸à¸­à¸  [{3}]
default.invalid.validator.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¸à¹à¸²à¸à¸à¸²à¸£à¸à¸§à¸à¸ªà¸­à¸à¸à¹à¸²à¸à¸µà¹à¸à¸±à¹à¸à¸à¸¶à¹à¸
default.not.inlist.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¹à¸à¹à¸­à¸¢à¸¹à¹à¹à¸à¸£à¸²à¸¢à¸à¸²à¸£à¸à¹à¸­à¹à¸à¸à¸µà¹  [{3}]
default.blank.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¹à¸à¹à¸à¸à¹à¸²à¸§à¹à¸²à¸à¹à¸à¹
default.not.equal.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¹à¸à¹à¸²à¸à¸±à¸ [{3}] à¹à¸à¹
default.null.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¹à¸à¹à¸ null à¹à¸à¹
default.not.unique.message=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ [{0}] à¸à¸­à¸à¸à¸¥à¸²à¸ª [{1}] à¸à¸¶à¹à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸ [{2}] à¸à¸°à¸à¹à¸­à¸à¹à¸¡à¹à¸à¹à¸³ (unique)

default.paginate.prev=à¸à¹à¸­à¸à¸«à¸à¹à¸²
default.paginate.next=à¸à¸±à¸à¹à¸
default.boolean.true=à¸à¸£à¸´à¸
default.boolean.false=à¹à¸à¹à¸
default.date.format=dd-MM-yyyy HH:mm:ss z
default.number.format=0

default.created.message=à¸ªà¸£à¹à¸²à¸ {0} {1} à¹à¸£à¸µà¸¢à¸à¸£à¹à¸­à¸¢à¹à¸¥à¹à¸§
default.updated.message=à¸à¸£à¸±à¸à¸à¸£à¸¸à¸ {0} {1} à¹à¸£à¸µà¸¢à¸à¸£à¹à¸­à¸¢à¹à¸¥à¹à¸§
default.deleted.message=à¸¥à¸ {0} {1} à¹à¸£à¸µà¸¢à¸à¸£à¹à¸­à¸¢à¹à¸¥à¹à¸§
default.not.deleted.message=à¹à¸¡à¹à¸ªà¸²à¸¡à¸²à¸£à¸à¸¥à¸ {0} {1}
default.not.found.message=à¹à¸¡à¹à¸à¸ {0} à¸à¹à¸§à¸¢ id {1} à¸à¸µà¹
default.optimistic.locking.failure=à¸¡à¸µà¸à¸¹à¹à¹à¸à¹à¸à¹à¸²à¸à¸­à¸·à¹à¸à¸à¸£à¸±à¸à¸à¸£à¸¸à¸ {0} à¸à¸à¸°à¸à¸µà¹à¸à¸¸à¸à¸à¸³à¸¥à¸±à¸à¹à¸à¹à¹à¸à¸à¹à¸­à¸¡à¸¹à¸¥à¸­à¸¢à¸¹à¹

default.home.label=à¸«à¸à¹à¸²à¹à¸£à¸
default.list.label=à¸£à¸²à¸¢à¸à¸²à¸£ {0}
default.add.label=à¹à¸à¸´à¹à¸¡ {0}
default.new.label=à¸ªà¸£à¹à¸²à¸ {0} à¹à¸«à¸¡à¹
default.create.label=à¸ªà¸£à¹à¸²à¸ {0}
default.show.label=à¹à¸ªà¸à¸ {0}
default.edit.label=à¹à¸à¹à¹à¸ {0}

default.button.create.label=à¸ªà¸£à¹à¸²à¸
default.button.edit.label=à¹à¸à¹à¹à¸
default.button.update.label=à¸à¸£à¸±à¸à¸à¸£à¸¸à¸
default.button.delete.label=à¸¥à¸
default.button.delete.confirm.message=à¸à¸¸à¸à¹à¸à¹à¹à¸à¸«à¸£à¸·à¸­à¹à¸¡à¹ ?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¹à¸à¹à¸à¸à¹à¸² URL à¸à¸µà¹à¸à¸¹à¸à¸à¹à¸­à¸
typeMismatch.java.net.URI=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¹à¸à¹à¸à¸à¹à¸² URI à¸à¸µà¹à¸à¸¹à¸à¸à¹à¸­à¸
typeMismatch.java.util.Date=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸à¸§à¸±à¸à¸à¸µà¹
typeMismatch.java.lang.Double=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸à¸à¸³à¸à¸§à¸à¸à¸£à¸°à¹à¸ à¸ Double
typeMismatch.java.lang.Integer=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸à¸à¸³à¸à¸§à¸à¸à¸£à¸°à¹à¸ à¸ Integer
typeMismatch.java.lang.Long=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸à¸à¸³à¸à¸§à¸à¸à¸£à¸°à¹à¸ à¸ Long
typeMismatch.java.lang.Short=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸à¸à¸³à¸à¸§à¸à¸à¸£à¸°à¹à¸ à¸ Short
typeMismatch.java.math.BigDecimal=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸à¸à¸³à¸à¸§à¸à¸à¸£à¸°à¹à¸ à¸ BigDecimal
typeMismatch.java.math.BigInteger=à¸à¸¸à¸à¸ªà¸¡à¸à¸±à¸à¸´ '{0}' à¸à¸°à¸à¹à¸­à¸à¸¡à¸µà¸à¹à¸²à¹à¸à¹à¸à¸à¸³à¸à¸§à¸à¸à¸£à¸°à¹à¸ à¸ BigInteger
