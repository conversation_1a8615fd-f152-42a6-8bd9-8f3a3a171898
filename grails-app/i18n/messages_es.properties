default.doesnt.match.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no corresponde al patrÃ³n [{3}]
default.invalid.url.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es una URL vÃ¡lida
default.invalid.creditCard.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es un nÃºmero de tarjeta de crÃ©dito vÃ¡lida
default.invalid.email.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es una direcciÃ³n de correo electrÃ³nico vÃ¡lida
default.invalid.range.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no entra en el rango vÃ¡lido de [{3}] a [{4}]
default.invalid.size.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no entra en el tamaÃ±o vÃ¡lido de [{3}] a [{4}]
default.invalid.max.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] excede el valor mÃ¡ximo [{3}]
default.invalid.min.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] es menos que el valor mÃ­nimo [{3}]
default.invalid.max.size.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] excede el tamaÃ±o mÃ¡ximo de [{3}]
default.invalid.min.size.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] es menor que el tamaÃ±o mÃ­nimo de [{3}]
default.invalid.validator.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no es vÃ¡lido
default.not.inlist.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no esta contenido dentro de la lista [{3}]
default.blank.message=La propiedad [{0}] de la clase [{1}] no puede ser vacÃ­a
default.not.equal.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] no puede igualar a [{3}]
default.null.message=La propiedad [{0}] de la clase [{1}] no puede ser nulo
default.not.unique.message=La propiedad [{0}] de la clase [{1}] con valor [{2}] debe ser Ãºnica

default.paginate.prev=Anterior
default.paginate.next=Siguiente
default.boolean.true=Verdadero
default.boolean.false=Falso
default.date.format=yyyy-MM-dd HH:mm:ss z
default.number.format=0

default.created.message={0} {1} creado
default.updated.message={0} {1} actualizado
default.deleted.message={0} {1} eliminado
default.not.deleted.message={0} {1} no puede eliminarse
default.not.found.message=No se encuentra {0} con id {1}
default.optimistic.locking.failure=Mientras usted editaba, otro usuario ha actualizado su {0}

default.home.label=Principal
default.list.label={0} Lista
default.add.label=Agregar {0}
default.new.label=Nuevo {0}
default.create.label=Crear {0}
default.show.label=Mostrar {0}
default.edit.label=Editar {0}

default.button.create.label=Crear
default.button.edit.label=Editar
default.button.update.label=Actualizar
default.button.delete.label=Eliminar
default.button.delete.confirm.message=Â¿EstÃ¡ usted seguro?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=La propiedad {0} debe ser una URL vÃ¡lida
typeMismatch.java.net.URI=La propiedad {0} debe ser una URI vÃ¡lida
typeMismatch.java.util.Date=La propiedad {0} debe ser una fecha vÃ¡lida
typeMismatch.java.lang.Double=La propiedad {0} debe ser un nÃºmero vÃ¡lido
typeMismatch.java.lang.Integer=La propiedad {0} debe ser un nÃºmero vÃ¡lido
typeMismatch.java.lang.Long=La propiedad {0} debe ser un nÃºmero vÃ¡lido
typeMismatch.java.lang.Short=La propiedad {0} debe ser un nÃºmero vÃ¡lido
typeMismatch.java.math.BigDecimal=La propiedad {0} debe ser un nÃºmero vÃ¡lido
typeMismatch.java.math.BigInteger=La propiedad {0} debe ser un nÃºmero vÃ¡lido