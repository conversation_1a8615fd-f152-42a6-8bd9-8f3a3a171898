#
# <NAME_EMAIL>, based on pt_BR translation by <PERSON>@gmail.com
#

default.doesnt.match.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o corresponde ao padrÃ£o definido [{3}]
default.invalid.url.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o Ã© um URL vÃ¡lido
default.invalid.creditCard.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o Ã© um nÃºmero vÃ¡lido de cartÃ£o de crÃ©dito
default.invalid.email.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o Ã© um endereÂÃ§o de email vÃ¡lido.
default.invalid.range.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o estÃ¡ dentro dos limites de valores vÃ¡lidos de [{3}] a [{4}]
default.invalid.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] estÃ¡ fora dos limites de tamanho vÃ¡lido de [{3}] a [{4}]
default.invalid.max.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o valor mÃ¡ximo [{3}]
default.invalid.min.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o atinge o valor mÃ­nimo [{3}]
default.invalid.max.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o tamanho mÃ¡ximo de [{3}]
default.invalid.min.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o atinge o tamanho mÃ­nimo de [{3}]
default.invalid.validator.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o passou na validaÂÃ§Ã£o
default.not.inlist.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o se encontra nos valores permitidos da lista [{3}]
default.blank.message=O campo [{0}] da classe [{1}] nÃ£o pode ser vazio
default.not.equal.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o pode ser igual a [{3}]
default.null.message=O campo [{0}] da classe [{1}] nÃ£o pode ser vazio
default.not.unique.message=O campo [{0}] da classe [{1}] com o valor [{2}] deve ser Ãºnico

default.paginate.prev=Anterior
default.paginate.next=PrÃ³ximo

# Mensagens de erro em atribuiÂÃ§Ã£o de valores. Use "typeMismatch.$className.$propertyName" para personalizar(eg typeMismatch.Book.author)
typeMismatch.java.net.URL=O campo {0} deve ser um URL vÃ¡lido.
typeMismatch.java.net.URI=O campo {0} deve ser um URI vÃ¡lido.
typeMismatch.java.util.Date=O campo {0} deve ser uma data vÃ¡lida
typeMismatch.java.lang.Double=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.lang.Integer=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.lang.Long=O campo {0} deve ser um nÃºmero valido.
typeMismatch.java.lang.Short=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.math.BigDecimal=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.math.BigInteger=O campo {0} deve ser um nÃºmero vÃ¡lido.
