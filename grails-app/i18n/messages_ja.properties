default.doesnt.match.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ã[{3}]ãã¿ã¼ã³ã¨ä¸è´ãã¦ãã¾ããã
default.invalid.url.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãæå¹ãªURLã§ã¯ããã¾ããã
default.invalid.creditCard.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãæå¹ãªã¯ã¬ã¸ããã«ã¼ãçªå·ã§ã¯ããã¾ããã
default.invalid.email.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãæå¹ãªã¡ã¼ã«ã¢ãã¬ã¹ã§ã¯ããã¾ããã
default.invalid.range.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ã[{3}]ãã[{4}]ç¯å²åãæå®ãã¦ãã ããã
default.invalid.size.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ã[{3}]ãã[{4}]ä»¥åãæå®ãã¦ãã ããã
default.invalid.max.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãæå¤§å¤[{3}]ããå¤§ããã§ãã
default.invalid.min.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãæå°å¤[{3}]ããå°ããã§ãã
default.invalid.max.size.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãæå¤§å¤[{3}]ããå¤§ããã§ãã
default.invalid.min.size.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãæå°å¤[{3}]ããå°ããã§ãã
default.invalid.validator.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ãã«ã¹ã¿ã ããªãã¼ã·ã§ã³ãééã§ãã¾ããã
default.not.inlist.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯ã[{3}]ãªã¹ãåã«å­å¨ãã¾ããã
default.blank.message=[{1}]ã¯ã©ã¹ã®ãã­ããã£[{0}]ã®ç©ºç½ã¯è¨±å¯ããã¾ããã
default.not.equal.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã«[{3}]ã¯è¨±å¯ããã¾ããã
default.null.message=[{1}]ã¯ã©ã¹ã®ãã­ããã£[{0}]ã«nullã¯è¨±å¯ããã¾ããã
default.not.unique.message=ã¯ã©ã¹[{1}]ãã­ããã£[{0}]ã®å¤[{2}]ã¯æ¢ã«ä½¿ç¨ããã¦ãã¾ãã

default.paginate.prev=æ»ã
default.paginate.next=æ¬¡ã¸
default.boolean.true=ã¯ã
default.boolean.false=ããã
default.date.format=yyyy/MM/dd HH:mm:ss z
default.number.format=0

default.created.message={0}(id:{1})ãä½æãã¾ããã
default.updated.message={0}(id:{1})ãæ´æ°ãã¾ããã
default.deleted.message={0}(id:{1})ãåé¤ãã¾ããã
default.not.deleted.message={0}(id:{1})ã¯åé¤ã§ãã¾ããã§ããã
default.not.found.message={0}(id:{1})ã¯è¦ã¤ããã¾ããã§ããã
default.optimistic.locking.failure=ãã®{0}ã¯ç·¨éä¸­ã«ä»ã®ã¦ã¼ã¶ã«ãã£ã¦åã«æ´æ°ããã¦ãã¾ãã

default.home.label=ãã¼ã 
default.list.label={0}ãªã¹ã
default.add.label={0}ãè¿½å 
default.new.label={0}ãæ°è¦ä½æ
default.create.label={0}ãä½æ
default.show.label={0}è©³ç´°
default.edit.label={0}ãç·¨é

default.button.create.label=ä½æ
default.button.edit.label=ç·¨é
default.button.update.label=æ´æ°
default.button.delete.label=åé¤
default.button.delete.confirm.message=æ¬å½ã«åé¤ãã¦ããããã§ãã?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL={0}ã¯æå¹ãªURLã§ãªããã°ãªãã¾ããã
typeMismatch.java.net.URI={0}ã¯æå¹ãªURIã§ãªããã°ãªãã¾ããã
typeMismatch.java.util.Date={0}ã¯æå¹ãªæ¥ä»ã§ãªããã°ãªãã¾ããã
typeMismatch.java.lang.Double={0}ã¯æå¹ãªæ°å¤ã§ãªããã°ãªãã¾ããã
typeMismatch.java.lang.Integer={0}ã¯æå¹ãªæ°å¤ã§ãªããã°ãªãã¾ããã
typeMismatch.java.lang.Long={0}ã¯æå¹ãªæ°å¤ã§ãªããã°ãªãã¾ããã
typeMismatch.java.lang.Short={0}ã¯æå¹ãªæ°å¤ã§ãªããã°ãªãã¾ããã
typeMismatch.java.math.BigDecimal={0}ã¯æå¹ãªæ°å¤ã§ãªããã°ãªãã¾ããã
typeMismatch.java.math.BigInteger={0}ã¯æå¹ãªæ°å¤ã§ãªããã°ãªãã¾ããã
