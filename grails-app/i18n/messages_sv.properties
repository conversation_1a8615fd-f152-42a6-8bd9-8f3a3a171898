default.doesnt.match.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] matchar inte mot uttrycket [{3}]
default.invalid.url.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r inte en giltig URL
default.invalid.creditCard.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r inte ett giltigt kreditkortsnummer
default.invalid.email.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r inte en giltig e-postadress
default.invalid.range.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r inte inom intervallet [{3}] till [{4}]
default.invalid.size.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] har en storlek som inte Ã¤r inom [{3}] till [{4}]
default.invalid.max.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¶verskrider maxvÃ¤rdet [{3}]
default.invalid.min.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r mindre Ã¤n minimivÃ¤rdet [{3}]
default.invalid.max.size.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¶verskrider maxstorleken [{3}]
default.invalid.min.size.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r mindre Ã¤n minimistorleken [{3}]
default.invalid.validator.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r inte giltigt enligt anpassad regel
default.not.inlist.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] Ã¤r inte giltigt, mÃ¥ste vara ett av [{3}]
default.blank.message=Attributet [{0}] fÃ¶r klassen [{1}] fÃ¥r inte vara tomt
default.not.equal.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] fÃ¥r inte vara lika med [{3}]
default.null.message=Attributet [{0}] fÃ¶r klassen [{1}] fÃ¥r inte vara tomt
default.not.unique.message=Attributet [{0}] fÃ¶r klassen [{1}] med vÃ¤rde [{2}] mÃ¥ste vara unikt

default.paginate.prev=FÃ¶regÃ¥ende
default.paginate.next=NÃ¤sta
default.boolean.true=Sant
default.boolean.false=Falskt
default.date.format=yyyy-MM-dd HH:mm:ss z
default.number.format=0

default.created.message={0} {1} skapades
default.updated.message={0} {1} uppdaterades
default.deleted.message={0} {1} borttagen
default.not.deleted.message={0} {1} kunde inte tas bort
default.not.found.message={0} med id {1} kunde inte hittas
default.optimistic.locking.failure=En annan anvÃ¤ndare har uppdaterat det hÃ¤r {0} objektet medan du redigerade det

default.home.label=Hem
default.list.label= {0} - Lista
default.add.label=LÃ¤gg till {0}
default.new.label=Skapa {0}
default.create.label=Skapa {0}
default.show.label=Visa {0}
default.edit.label=Ãndra {0}

default.button.create.label=Skapa
default.button.edit.label=Ãndra
default.button.update.label=Uppdatera
default.button.delete.label=Ta bort
default.button.delete.confirm.message=Ãr du sÃ¤ker?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=VÃ¤rdet fÃ¶r {0} mÃ¥ste vara en giltig URL
typeMismatch.java.net.URI=VÃ¤rdet fÃ¶r {0} mÃ¥ste vara en giltig URI
typeMismatch.java.util.Date=VÃ¤rdet {0} mÃ¥ste vara ett giltigt datum
typeMismatch.java.lang.Double=VÃ¤rdet {0} mÃ¥ste vara ett giltigt nummer
typeMismatch.java.lang.Integer=VÃ¤rdet {0} mÃ¥ste vara ett giltigt heltal
typeMismatch.java.lang.Long=VÃ¤rdet {0} mÃ¥ste vara ett giltigt heltal
typeMismatch.java.lang.Short=VÃ¤rdet {0} mÃ¥ste vara ett giltigt heltal
typeMismatch.java.math.BigDecimal=VÃ¤rdet {0} mÃ¥ste vara ett giltigt nummer
typeMismatch.java.math.BigInteger=VÃ¤rdet {0} mÃ¥ste vara ett giltigt heltal