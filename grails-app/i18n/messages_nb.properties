default.doesnt.match.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] overholder ikke mÃ¸nsteret [{3}]
default.invalid.url.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] er ikke en gyldig URL
default.invalid.creditCard.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] er ikke et gyldig kredittkortnummer
default.invalid.email.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] er ikke en gyldig epostadresse
default.invalid.range.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] er ikke innenfor intervallet [{3}] til [{4}]
default.invalid.size.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] er ikke innenfor intervallet [{3}] til [{4}]
default.invalid.max.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] overstiger maksimumsverdien pÃ¥ [{3}]
default.invalid.min.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] er under minimumsverdien pÃ¥ [{3}]
default.invalid.max.size.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] overstiger maksimumslengden pÃ¥ [{3}]
default.invalid.min.size.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] er kortere enn minimumslengden pÃ¥ [{3}]
default.invalid.validator.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] overholder ikke den brukerdefinerte valideringen
default.not.inlist.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] finnes ikke i listen [{3}]
default.blank.message=Feltet [{0}] i klassen [{1}] kan ikke vÃ¦re tom
default.not.equal.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] kan ikke vÃ¦re [{3}]
default.null.message=Feltet [{0}] i klassen [{1}] kan ikke vÃ¦re null
default.not.unique.message=Feltet [{0}] i klassen [{1}] med verdien [{2}] mÃ¥ vÃ¦re unik

default.paginate.prev=Forrige
default.paginate.next=Neste
default.boolean.true=Ja
default.boolean.false=Nei
default.date.format=dd.MM.yyyy HH:mm:ss z
default.number.format=0

default.created.message={0} {1} opprettet
default.updated.message={0} {1} oppdatert
default.deleted.message={0} {1} slettet
default.not.deleted.message={0} {1} kunne ikke slettes
default.not.found.message={0} med id {1} ble ikke funnet
default.optimistic.locking.failure=En annen bruker har oppdatert denne {0} mens du redigerte

default.home.label=Hjem
default.list.label={0}liste
default.add.label=Legg til {0}
default.new.label=Ny {0}
default.create.label=Opprett {0}
default.show.label=Vis {0}
default.edit.label=Endre {0}

default.button.create.label=Opprett
default.button.edit.label=Endre
default.button.update.label=Oppdater
default.button.delete.label=Slett
default.button.delete.confirm.message=Er du sikker?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=Feltet {0} mÃ¥ vÃ¦re en gyldig URL
typeMismatch.java.net.URI=Feltet {0} mÃ¥ vÃ¦re en gyldig URI
typeMismatch.java.util.Date=Feltet {0} mÃ¥ vÃ¦re en gyldig dato
typeMismatch.java.lang.Double=Feltet {0} mÃ¥ vÃ¦re et gyldig tall
typeMismatch.java.lang.Integer=Feltet {0} mÃ¥ vÃ¦re et gyldig heltall
typeMismatch.java.lang.Long=Feltet {0} mÃ¥ vÃ¦re et gyldig heltall
typeMismatch.java.lang.Short=Feltet {0} mÃ¥ vÃ¦re et gyldig heltall
typeMismatch.java.math.BigDecimal=Feltet {0} mÃ¥ vÃ¦re et gyldig tall
typeMismatch.java.math.BigInteger=Feltet {0} mÃ¥ vÃ¦re et gyldig heltall

