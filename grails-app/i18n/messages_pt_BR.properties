#
# Translated by <PERSON>@gmail.com
#

default.doesnt.match.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o atende ao padrÃ£o definido [{3}]
default.invalid.url.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o Ã© uma URL vÃ¡lida
default.invalid.creditCard.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o Ã© um nÃºmero vÃ¡lido de cartÃ£o de crÃ©dito
default.invalid.email.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o Ã© um endereÃ§o de email vÃ¡lido.
default.invalid.range.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o estÃ¡ entre a faixa de valores vÃ¡lida de [{3}] atÃ© [{4}]
default.invalid.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o estÃ¡ na faixa de tamanho vÃ¡lida de [{3}] atÃ© [{4}]
default.invalid.max.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o valor mÃ¡ximo [{3}]
default.invalid.min.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o atinge o valor mÃ­nimo [{3}]
default.invalid.max.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o tamanho mÃ¡ximo de [{3}]
default.invalid.min.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o atinge o tamanho mÃ­nimo de [{3}]
default.invalid.validator.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o passou na validaÃ§Ã£o
default.not.inlist.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o Ã© um valor dentre os permitidos na lista [{3}]
default.blank.message=O campo [{0}] da classe [{1}] nÃ£o pode ficar em branco
default.not.equal.message=O campo [{0}] da classe [{1}] com o valor [{2}] nÃ£o pode ser igual a [{3}]
default.null.message=O campo [{0}] da classe [{1}] nÃ£o pode ser vazio
default.not.unique.message=O campo [{0}] da classe [{1}] com o valor [{2}] deve ser Ãºnico

default.paginate.prev=Anterior
default.paginate.next=PrÃ³ximo
default.boolean.true=Sim
default.boolean.false=NÃ£o
default.date.format=dd/MM/yyyy HH:mm:ss z
default.number.format=0

default.created.message={0} {1} criado
default.updated.message={0} {1} atualizado
default.deleted.message={0} {1} removido
default.not.deleted.message={0} {1} nÃ£o pode ser removido
default.not.found.message={0} nÃ£o foi encontrado com o id {1}
default.optimistic.locking.failure=Outro usuÃ¡rio atualizou este [{0}] enquanto vocÃª tentou salvÃ¡-lo

default.home.label=Principal
default.list.label={0} Listagem
default.add.label=Adicionar {0}
default.new.label=Novo {0}
default.create.label=Criar {0}
default.show.label=Ver {0}
default.edit.label=Editar {0}

default.button.create.label=Criar
default.button.edit.label=Editar
default.button.update.label=Alterar
default.button.delete.label=Remover
default.button.delete.confirm.message=Tem certeza?

# Mensagens de erro em atribuiÃ§Ã£o de valores. Use "typeMismatch.$className.$propertyName" para customizar (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=O campo {0} deve ser uma URL vÃ¡lida.
typeMismatch.java.net.URI=O campo {0} deve ser uma URI vÃ¡lida.
typeMismatch.java.util.Date=O campo {0} deve ser uma data vÃ¡lida
typeMismatch.java.lang.Double=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.lang.Integer=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.lang.Long=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.lang.Short=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.math.BigDecimal=O campo {0} deve ser um nÃºmero vÃ¡lido.
typeMismatch.java.math.BigInteger=O campo {0} deve ser um nÃºmero vÃ¡lido.
