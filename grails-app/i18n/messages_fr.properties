default.doesnt.match.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] ne correspond pas au pattern [{3}]
default.invalid.url.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] n'est pas une URL valide
default.invalid.creditCard.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] n'est pas un numÃ©ro de carte de crÃ©dit valide
default.invalid.email.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] n'est pas une adresse e-mail valide
default.invalid.range.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] n'est pas contenue dans l'intervalle [{3}] Ã  [{4}]
default.invalid.size.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] n'est pas contenue dans l'intervalle [{3}] Ã  [{4}]
default.invalid.max.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] est supÃ©rieure Ã  la valeur maximum [{3}]
default.invalid.min.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] est infÃ©rieure Ã  la valeur minimum [{3}]
default.invalid.max.size.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] est supÃ©rieure Ã  la valeur maximum [{3}]
default.invalid.min.size.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] est infÃ©rieure Ã  la valeur minimum [{3}]
default.invalid.validator.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] n'est pas valide
default.not.inlist.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] ne fait pas partie de la liste [{3}]
default.blank.message=La propriÃ©tÃ© [{0}] de la classe [{1}] ne peut pas Ãªtre vide
default.not.equal.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] ne peut pas Ãªtre Ã©gale Ã  [{3}]
default.null.message=La propriÃ©tÃ© [{0}] de la classe [{1}] ne peut pas Ãªtre nulle
default.not.unique.message=La propriÃ©tÃ© [{0}] de la classe [{1}] avec la valeur [{2}] doit Ãªtre unique

default.paginate.prev=PrÃ©cÃ©dent
default.paginate.next=Suivant
