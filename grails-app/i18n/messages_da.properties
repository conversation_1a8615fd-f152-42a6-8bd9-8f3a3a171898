default.doesnt.match.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] overholder ik<PERSON> mÃ¸nsteret [{3}]
default.invalid.url.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] er ikke en gyldig URL
default.invalid.creditCard.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] er ikke et gyldigt kreditkortnummer
default.invalid.email.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] er ikke en gyldig e-mail adresse
default.invalid.range.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] ligger ikke inden for intervallet fra  [{3}] til [{4}]
default.invalid.size.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] ligger ikke inden for stÃ¸rrelsen fra [{3}] til [{4}]
default.invalid.max.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] overstiger den maksimale vÃ¦rdi [{3}]
default.invalid.min.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] er under den minimale vÃ¦rdi [{3}]
default.invalid.max.size.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] overstiger den maksimale stÃ¸rrelse pÃ¥ [{3}]
default.invalid.min.size.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] er under den minimale stÃ¸rrelse pÃ¥ [{3}]
default.invalid.validator.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] overholder ikke den brugerdefinerede validering
default.not.inlist.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] findes ikke i listen [{3}]
default.blank.message=Feltet [{0}] i klassen [{1}] kan ikke vÃ¦re tom
default.not.equal.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] mÃ¥ ikke vÃ¦re [{3}]
default.null.message=Feltet [{0}] i klassen [{1}] kan ikke vÃ¦re null
default.not.unique.message=Feltet [{0}] i klassen [{1}] som har vÃ¦rdien [{2}] skal vÃ¦re unik

default.paginate.prev=Forrige
default.paginate.next=NÃ¦ste
default.boolean.true=Sand
default.boolean.false=Falsk
default.date.format=yyyy-MM-dd HH:mm:ss z
default.number.format=0

default.created.message={0} {1} oprettet
default.updated.message={0} {1} opdateret
default.deleted.message={0} {1} slettet
default.not.deleted.message={0} {1} kunne ikke slettes
default.not.found.message={0} med id {1} er ikke fundet
default.optimistic.locking.failure=En anden bruger har opdateret denne {0} imens du har lavet rettelser

default.home.label=Hjem
default.list.label={0} Liste
default.add.label=TilfÃ¸j {0}
default.new.label=Ny {0}
default.create.label=Opret {0}
default.show.label=Vis {0}
default.edit.label=Ret {0}

default.button.create.label=Opret
default.button.edit.label=Ret
default.button.update.label=Opdater
default.button.delete.label=Slet
default.button.delete.confirm.message=Er du sikker?

# Databindingsfejl. Brug "typeMismatch.$className.$propertyName for at passe til en given klasse (f.eks typeMismatch.Book.author)
typeMismatch.java.net.URL=Feltet {0} skal vÃ¦re en valid URL
typeMismatch.java.net.URI=Feltet {0} skal vÃ¦re en valid URI
typeMismatch.java.util.Date=Feltet {0} skal vÃ¦re en valid Dato
typeMismatch.java.lang.Double=Feltet {0} skal vÃ¦re et valid tal
typeMismatch.java.lang.Integer=Feltet {0} skal vÃ¦re et valid tal
typeMismatch.java.lang.Long=Feltet {0} skal vÃ¦re et valid tal
typeMismatch.java.lang.Short=Feltet {0} skal vÃ¦re et valid tal
typeMismatch.java.math.BigDecimal=Feltet {0} skal vÃ¦re et valid tal
typeMismatch.java.math.BigInteger=Feltet {0} skal vÃ¦re et valid tal

