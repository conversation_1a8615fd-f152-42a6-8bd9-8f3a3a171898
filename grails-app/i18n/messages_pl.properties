#
# Translated by <PERSON> - <EMAIL>
#

default.doesnt.match.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] nie pasuje do wymaganego wzorca [{3}]
default.invalid.url.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] jest niepoprawnym adresem URL
default.invalid.creditCard.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] with value [{2}] nie jest poprawnym numerem karty kredytowej
default.invalid.email.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] nie jest poprawnym adresem e-mail
default.invalid.range.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] nie zawiera siÄ zakÅadanym zakresie od [{3}] do [{4}]
default.invalid.size.message=<PERSON><PERSON>aÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] nie zawiera siÄ w zakÅadanym zakresie rozmiarÃ³w od [{3}] do [{4}]
default.invalid.max.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] przekracza maksymalnÄ wartoÅÄ [{3}]
default.invalid.min.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] jest mniejsza niÅ¼ minimalna wartoÅÄ [{3}]
default.invalid.max.size.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] przekracza maksymalny rozmiar [{3}]
default.invalid.min.size.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] jest mniejsza niÅ¼ minimalny rozmiar [{3}]
default.invalid.validator.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] nie speÅnia zaÅoÅ¼onych niestandardowych warunkÃ³w
default.not.inlist.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] nie zawiera siÄ w liÅcie [{3}]
default.blank.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] nie moÅ¼e byÄ pusta
default.not.equal.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] nie moÅ¼e rÃ³wnaÄ siÄ [{3}]
default.null.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] nie moÅ¼e byÄ null
default.not.unique.message=WÅaÅciwoÅÄ [{0}] klasy [{1}] o wartoÅci [{2}] musi byÄ unikalna

default.paginate.prev=Poprzedni
default.paginate.next=NastÄpny
default.boolean.true=Prawda
default.boolean.false=FaÅsz
default.date.format=yyyy-MM-dd HH:mm:ss z
default.number.format=0

default.created.message=Utworzono {0} {1}
default.updated.message=Zaktualizowano {0} {1}
default.deleted.message=UsuniÄto {0} {1}
default.not.deleted.message={0} {1} nie mÃ³gÅ zostaÄ usuniÄty
default.not.found.message=Nie znaleziono {0} o id {1}
default.optimistic.locking.failure=Inny uÅ¼ytkownik zaktualizowaÅ ten obiekt {0} w trakcie twoich zmian

default.home.label=Strona domowa
default.list.label=Lista {0}
default.add.label=Dodaj {0}
default.new.label=UtwÃ³rz {0}
default.create.label=UtwÃ³rz {0}
default.show.label=PokaÅ¼ {0}
default.edit.label=Edytuj {0}

default.button.create.label=UtwÃ³rz
default.button.edit.label=Edytuj
default.button.update.label=Zaktualizuj
default.button.delete.label=UsuÅ
default.button.delete.confirm.message=Czy jesteÅ pewien?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=WÅaÅciwoÅÄ {0} musi byÄ poprawnym adresem URL
typeMismatch.java.net.URI=WÅaÅciwoÅÄ {0} musi byÄ poprawnym adresem URI
typeMismatch.java.util.Date=WÅaÅciwoÅÄ {0} musi byÄ poprawnÄ datÄ
typeMismatch.java.lang.Double=WÅaÅciwoÅÄ {0} musi byÄ poprawnyÄ liczbÄ
typeMismatch.java.lang.Integer=WÅaÅciwoÅÄ {0} musi byÄ poprawnyÄ liczbÄ
typeMismatch.java.lang.Long=WÅaÅciwoÅÄ {0} musi byÄ poprawnyÄ liczbÄ
typeMismatch.java.lang.Short=WÅaÅciwoÅÄ {0} musi byÄ poprawnyÄ liczbÄ
typeMismatch.java.math.BigDecimal=WÅaÅciwoÅÄ {0} musi byÄ poprawnyÄ liczbÄ
typeMismatch.java.math.BigInteger=WÅaÅciwoÅÄ {0} musi byÄ poprawnyÄ liczbÄ
