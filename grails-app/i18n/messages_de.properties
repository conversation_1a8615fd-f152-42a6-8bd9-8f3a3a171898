default.doesnt.match.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] entspricht nicht dem vorgegebenen Muster [{3}]
default.invalid.url.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist keine gÃ¼ltige URL
default.invalid.creditCard.message=Das Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist keine gÃ¼ltige Kreditkartennummer
default.invalid.email.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist keine gÃ¼ltige E-Mail Adresse
default.invalid.range.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist nicht im Wertebereich von [{3}] bis [{4}]
default.invalid.size.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist nicht im Wertebereich von [{3}] bis [{4}]
default.invalid.max.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist grÃ¶Ãer als der HÃ¶chstwert von [{3}]
default.invalid.min.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist kleiner als der Mindestwert von [{3}]
default.invalid.max.size.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] Ã¼bersteigt den HÃ¶chstwert von [{3}]
default.invalid.min.size.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] unterschreitet den Mindestwert von [{3}]
default.invalid.validator.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist ungÃ¼ltig
default.not.inlist.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] ist nicht in der Liste [{3}] enthalten.
default.blank.message=Die Eigenschaft [{0}] des Typs [{1}] darf nicht leer sein
default.not.equal.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] darf nicht gleich [{3}] sein
default.null.message=Die Eigenschaft [{0}] des Typs [{1}] darf nicht null sein
default.not.unique.message=Die Eigenschaft [{0}] des Typs [{1}] mit dem Wert [{2}] darf nur einmal vorkommen

default.paginate.prev=Vorherige
default.paginate.next=NÃ¤chste
default.boolean.true=Wahr
default.boolean.false=Falsch
default.date.format=dd.MM.yyyy HH:mm:ss z
default.number.format=0

default.created.message={0} {1} wurde angelegt
default.updated.message={0} {1} wurde geÃ¤ndert
default.deleted.message={0} {1} wurde gelÃ¶scht
default.not.deleted.message={0} {1} konnte nicht gelÃ¶scht werden
default.not.found.message={0} mit der id {1} wurde nicht gefunden
default.optimistic.locking.failure=Ein anderer Benutzer hat das {0} Object geÃ¤ndert wÃ¤hrend Sie es bearbeitet haben

default.home.label=Home
default.list.label={0} Liste
default.add.label={0} hinzufÃ¼gen
default.new.label={0} anlegen
default.create.label={0} anlegen
default.show.label={0} anzeigen
default.edit.label={0} bearbeiten

default.button.create.label=Anlegen
default.button.edit.label=Bearbeiten
default.button.update.label=Aktualisieren
default.button.delete.label=LÃ¶schen
default.button.delete.confirm.message=Sind Sie sicher?

# Data binding errors. Use "typeMismatch.$className.$propertyName to customize (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=Die Eigenschaft {0} muss eine gÃ¼ltige URL sein
typeMismatch.java.net.URI=Die Eigenschaft {0} muss eine gÃ¼ltige URI sein
typeMismatch.java.util.Date=Die Eigenschaft {0} muss ein gÃ¼ltiges Datum sein
typeMismatch.java.lang.Double=Die Eigenschaft {0} muss eine gÃ¼ltige Zahl sein
typeMismatch.java.lang.Integer=Die Eigenschaft {0} muss eine gÃ¼ltige Zahl sein
typeMismatch.java.lang.Long=Die Eigenschaft {0} muss eine gÃ¼ltige Zahl sein
typeMismatch.java.lang.Short=Die Eigenschaft {0} muss eine gÃ¼ltige Zahl sein
typeMismatch.java.math.BigDecimal=Die Eigenschaft {0} muss eine gÃ¼ltige Zahl sein
typeMismatch.java.math.BigInteger=Die Eigenschaft {0} muss eine gÃ¼ltige Zahl sein
