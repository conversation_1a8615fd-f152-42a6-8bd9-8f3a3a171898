package com.wonderslate_v2

import grails.gorm.transactions.Transactional
import com.wonderslate_v2.usermanagement.User
import com.wonderslate_v2.usermanagement.Role
import com.wonderslate_v2.usermanagement.UserRole
import grails.plugin.springsecurity.SpringSecurityService

class AuthenticationService {
    SpringSecurityService springSecurityService

    def serviceMethod() {

    }

    @Transactional('wsuser')
    def authenticate(String rawUsername, String rawPassword, def session) {
        String username = "1_" + rawUsername
        println(username)
        def user = User.findByUsername(username)

        if (!user) {
            throw new RuntimeException("User not found")
        }

        // Check if password encoder is available and password matches
        if (springSecurityService?.passwordEncoder) {
            String encodedPassword = user.password

            // Handle legacy passwords without algorithm prefix
            if (encodedPassword && !encodedPassword.startsWith('{')) {
                // Add bcrypt prefix for legacy passwords
                encodedPassword = '{bcrypt}' + encodedPassword
            }

            if (!springSecurityService.passwordEncoder.matches(rawPassword, encodedPassword)) {
                throw new RuntimeException("Invalid password")
            }
            if(springSecurityService.passwordEncoder.matches(rawPassword, encodedPassword)){
                session.user = user
            }
        } else {
            println("Password encoder not available")
            if (user.password != rawPassword) {
                throw new RuntimeException("Invalid password")
            }
        }

        return user
    }

    @Transactional('wsuser')
    def registerUser(String name, String rawUsername, String email, String password, String confirmPassword) {
        // Validation
        if (!name?.trim()) {
            throw new RuntimeException("Name is required")
        }
        if (!rawUsername?.trim()) {
            throw new RuntimeException("Username is required")
        }
        if (!email?.trim()) {
            throw new RuntimeException("Email is required")
        }
        if (!password?.trim()) {
            throw new RuntimeException("Password is required")
        }
        if (!confirmPassword?.trim()) {
            throw new RuntimeException("Confirm password is required")
        }
        if (password != confirmPassword) {
            throw new RuntimeException("Passwords do not match")
        }
        if (password.length() < 6) {
            throw new RuntimeException("Password must be at least 6 characters long")
        }

        // Email validation
        if (!email.matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
            throw new RuntimeException("Invalid email format")
        }

        // Add prefix to username like in authenticate method
        String username = "1_" + rawUsername

        // Check if username already exists
        if (User.findByUsername(username)) {
            throw new RuntimeException("Username already exists")
        }

        // Check if email already exists
        if (User.findByEmail(email)) {
            throw new RuntimeException("Email already exists")
        }

        def hashPassword = springSecurityService.encodePassword(password)
        hashPassword = hashPassword.replace("{bcrypt}", "")

        // Create new user
        def user = new User(
            username: username,
            password: hashPassword,
            name: name,
            email: email,
            enabled: true,
            accountExpired: false,
            accountLocked: false,
            passwordExpired: false
        )

        if (!user.save(flush: true)) {
            throw new RuntimeException("Failed to create user: ${user.errors}")
        }

        // Assign ROLE_USER to the new user
        def userRole = Role.findByAuthority('ROLE_USER')
        UserRole.create(user, userRole, true)

        return user
    }
}
